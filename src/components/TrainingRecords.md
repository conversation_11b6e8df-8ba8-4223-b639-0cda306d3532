# TrainingRecords 训练记录组件

## 概述

TrainingRecords 是一个用于显示训练记录的Vue组件，支持时间轴样式展示、图片懒加载、图片预览等功能。已从uniapp适配为Web环境。

## 功能特性

- ✅ 时间轴样式的记录展示
- ✅ 图片懒加载优化性能
- ✅ 横向滚动图片浏览
- ✅ 图片预览功能（使用Vant ImagePreview）
- ✅ 状态图标自动显示（合格/不合格）
- ✅ 响应式设计
- ✅ TypeScript支持

## 使用方法

```vue
<template>
  <TrainingRecords :records="trainingRecords" title="我的训练记录" />
</template>

<script setup lang="ts">
import TrainingRecords from '@/components/TrainingRecords.vue'

const trainingRecords = [
  {
    date: '2024-01-15',
    workPassRate: '95',
    workCount: '10',
    workFailCount: '1',
    actionRate: '88',
    actionFailCount: '2',
    images: [
      {
        imageUrl: 'https://example.com/image1.jpg',
        description: '正常操作',
        time: '09:30',
        iconImg: '', // 可选，留空将使用本地状态图标
        isQualified: true, // 决定显示哪个状态图标
        recordDetailAlarmId: 'alarm123', // 用于懒加载
        loaded: false, // 是否已加载
      }
    ]
  }
]
</script>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '训练记录' | 组件标题 |
| records | Array | [] | 训练记录数据数组 |

## 数据结构

### TrainingRecord

```typescript
interface TrainingRecord {
  date: string                    // 日期
  workPassRate: string           // 作业合格率
  workCount: string              // 作业次数
  workFailCount: string          // 作业不合格次数
  actionRate: string             // 动作达标率
  actionFailCount: string        // 动作不达标次数
  images?: {
    imageUrl: string             // 图片URL
    description: string          // 图片描述
    time: string                // 时间
    iconImg: string             // 自定义图标URL（可选）
    isQualified?: boolean       // 是否合格（决定状态图标）
    recordDetailAlarmId?: string // 图片ID（用于懒加载）
    loaded?: boolean            // 是否已加载
  }[]
  scrollLeft?: number           // 滚动位置（内部使用）
  activeImgIndex?: number       // 当前图片索引（内部使用）
  visibleImageRange?: [number, number] // 可见图片范围（内部使用）
}
```

## 状态图标

组件会根据图片的 `isQualified` 属性自动显示对应的状态图标：

- `isQualified: true` 或 `undefined` → 显示 `icon-standard.png`（合格图标）
- `isQualified: false` → 显示 `icon-unqualified.png`（不合格图标）

如果提供了 `iconImg` 属性且不为空，将优先使用自定义图标。

## 图片懒加载

组件支持图片懒加载功能：

1. 只有在可视范围内的图片才会被加载
2. 使用 `recordDetailAlarmId` 通过API获取图片URL
3. 加载失败时显示默认图片

## 样式定制

组件使用了UnoCSS/TailwindCSS类名，可以通过以下方式定制样式：

- 修改颜色变量（如 `#00996B` 主题色）
- 调整时间轴样式
- 自定义图片容器样式

## 依赖

- Vue 3
- Vant 4
- UnoCSS/TailwindCSS
- TypeScript

## 注意事项

1. 确保 `assets/img/` 目录下有对应的状态图标文件
2. 图片预览功能依赖Vant的ImagePreview组件
3. 组件已适配Web环境，不再依赖uniapp API
