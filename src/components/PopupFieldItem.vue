<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import type { FieldRule } from 'vant'

export interface PopupFieldProps {
  label?: string
  placeholder?: string

  visible?: boolean
  lazyRender?: boolean

  disabled?: boolean

  content?: string

  rules?: FieldRule[]
}

const props = defineProps<PopupFieldProps>()

const emit = defineEmits(['update:visible'])

const vShow = useVModel(props, 'visible', emit, {
  passive: true,
})

function open() {
  vShow.value = true
}
</script>

<template>
  <VanField
    is-link
    readonly
    :label="label"
    :model-value="content"
    :placeholder="placeholder || `请选择`"
    :disabled="disabled"
    :rules="rules"
    @click="open()"
  />
  <VanPopup
    v-model:show="vShow"
    round
    position="bottom"
    :lazyRender="lazyRender"
    :z-index="999999"
  >
    <slot> </slot>
  </VanPopup>
</template>

<style lang="less" scoped></style>
