<script setup>
import { computed, defineEmits, defineProps, ref } from 'vue'

/**
 * CircleProgress 圆环进度组件
 * @property {string} mode 模式，可选值：timing定时器，progress进度条
 * @property {string} activeColor 进度条激活颜色
 * @property {string} defaultColor 进度条默认颜色
 * @property {string} centerBgColor 圆环中心背景色
 * @property {number} size 组件尺寸（px）
 * @property {number} strokeWidth 圆环宽度（px）
 * @property {number} startAngle 进度开始角度（度）
 * @property {number} duration 定时器模式的持续时间（秒）
 * @property {number} progress 进度值（0-100）
 * @property {boolean} showText 是否显示进度文本
 * @property {string} textColor 文本颜色
 * @property {boolean} animated 是否启用动画
 * @event {Function} complete 完成时的回调
 * @event {Function} update 进度更新时的回调
 */

const props = defineProps({
  mode: {
    type: String,
    default: 'progress',
    validator: value => ['timing', 'progress'].includes(value),
  },
  activeColor: {
    type: String,
    default: '#409eff',
  },
  defaultColor: {
    type: String,
    default: '#e4e7ed',
  },
  centerBgColor: {
    type: String,
    default: '#ffffff',
  },
  size: {
    type: Number,
    default: 120,
  },
  strokeWidth: {
    type: Number,
    default: 8,
  },
  startAngle: {
    type: Number,
    default: -90,
  },
  duration: {
    type: Number,
    default: 3,
  },
  progress: {
    type: Number,
    default: 0,
    validator: value => value >= 0 && value <= 100,
  },
  showText: {
    type: Boolean,
    default: true,
  },
  textColor: {
    type: String,
    default: '#606266',
  },
  animated: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['complete', 'update'])

// 响应式数据
const isRunning = ref(false)
const currentProgress = ref(props.progress)
const animationId = ref(null)

// 计算属性
const radius = computed(() => props.size / 2)
const strokeRadius = computed(() => radius.value - props.strokeWidth / 2)
const circumference = computed(() => 2 * Math.PI * strokeRadius.value)
const strokeDasharray = computed(() => circumference.value)
const strokeDashoffset = computed(() => {
  const progress = props.mode === 'timing' ? currentProgress.value : props.progress
  return circumference.value - (progress / 100) * circumference.value
})

const containerStyles = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  transform: `rotate(${props.startAngle}deg)`,
}))

const progressText = computed(() => {
  const progress = props.mode === 'timing' ? currentProgress.value : props.progress
  return `${Math.round(progress)}%`
})

// 方法
function start() {
  if (props.mode !== 'timing')
    return

  if (isRunning.value)
    return

  isRunning.value = true
  currentProgress.value = 0

  const startTime = Date.now()
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min((elapsed / (props.duration * 1000)) * 100, 100)

    currentProgress.value = progress
    emit('update', progress)

    if (progress < 100) {
      animationId.value = requestAnimationFrame(animate)
    }
    else {
      isRunning.value = false
      emit('complete')
    }
  }

  if (props.animated) {
    animationId.value = requestAnimationFrame(animate)
  }
  else {
    currentProgress.value = 100
    emit('update', 100)
    emit('complete')
    isRunning.value = false
  }
}

function stop() {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
  isRunning.value = false
}

function reset() {
  stop()
  currentProgress.value = 0
  emit('update', 0)
}

// 暴露方法给父组件
defineExpose({
  start,
  stop,
  reset,
  isRunning: () => isRunning.value,
  currentProgress: () => currentProgress.value,
})
</script>

<template>
  <div class="circle-progress" :style="containerStyles">
    <svg
      :width="props.size"
      :height="props.size"
      class="circle-progress__svg"
    >
      <!-- 背景圆环 -->
      <circle
        :cx="radius"
        :cy="radius"
        :r="strokeRadius"
        :stroke="props.defaultColor"
        :stroke-width="props.strokeWidth"
        fill="none"
        class="circle-progress__background"
      />
      <!-- 进度圆环 -->
      <circle
        :cx="radius"
        :cy="radius"
        :r="strokeRadius"
        :stroke="props.activeColor"
        :stroke-width="props.strokeWidth"
        :stroke-dasharray="strokeDasharray"
        :stroke-dashoffset="strokeDashoffset"
        fill="none"
        stroke-linecap="round"
        class="circle-progress__progress"
        :class="{ 'circle-progress__progress--animated': props.animated }"
      />
    </svg>

    <!-- 中心内容区域 -->
    <div
      class="circle-progress__content"
      :style="{
        backgroundColor: props.centerBgColor,
        color: props.textColor,
        width: `${props.size - props.strokeWidth * 2}px`,
        height: `${props.size - props.strokeWidth * 2}px`,
      }"
    >
      <slot>
        <span v-if="props.showText" class="circle-progress__text">
          {{ progressText }}
        </span>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.circle-progress {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.circle-progress__svg {
  transform: rotate(-90deg);
}

.circle-progress__background {
  opacity: 0.3;
}

.circle-progress__progress {
  transition: stroke-dashoffset 0.3s ease;
  transform-origin: center;
}

.circle-progress__progress--animated {
  transition: stroke-dashoffset 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.circle-progress__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-sizing: border-box;
}

.circle-progress__text {
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
}

/* 响应式字体大小 */
.circle-progress__content[style*='width: 40px'] .circle-progress__text,
.circle-progress__content[style*='width: 48px'] .circle-progress__text {
  font-size: 10px;
}

.circle-progress__content[style*='width: 56px'] .circle-progress__text,
.circle-progress__content[style*='width: 64px'] .circle-progress__text {
  font-size: 12px;
}

.circle-progress__content[style*='width: 104px'] .circle-progress__text,
.circle-progress__content[style*='width: 120px'] .circle-progress__text {
  font-size: 16px;
}

.circle-progress__content[style*='width: 136px'] .circle-progress__text,
.circle-progress__content[style*='width: 152px'] .circle-progress__text {
  font-size: 18px;
}
</style>
