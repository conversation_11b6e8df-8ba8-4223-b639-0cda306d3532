<script setup lang="ts">
import { ref } from 'vue'
import { previewImages, previewImagesWithVant, previewImagesWithCustomPage } from '@/utils/imagePreview'
import type { ImagePreviewItem } from '@/utils/imagePreview'

// 示例图片数据
const sampleImages = ref<ImagePreviewItem[]>([
  {
    url: 'https://picsum.photos/800/600?random=1',
    description: '这是第一张示例图片，用于测试图片预览功能',
    time: '2024-01-15 10:30:00',
    iconImg: 'https://picsum.photos/64/64?random=icon1',
    isQualified: true
  },
  {
    url: 'https://picsum.photos/800/600?random=2',
    description: '这是第二张示例图片，描述可能会比较长一些，用来测试文本换行效果和多行显示',
    time: '2024-01-15 11:45:00',
    iconImg: 'https://picsum.photos/64/64?random=icon2',
    isQualified: false
  },
  {
    url: 'https://picsum.photos/800/600?random=3',
    description: '第三张图片',
    time: '2024-01-15 14:20:00',
    isQualified: true
  },
  {
    url: 'https://picsum.photos/800/600?random=4',
    time: '2024-01-15 16:10:00',
    isQualified: true
  },
  {
    url: 'https://picsum.photos/800/600?random=5',
    description: '最后一张图片，没有时间信息',
    isQualified: false
  }
])

// 简单的图片URL数组
const simpleImages = ref([
  'https://picsum.photos/800/600?random=6',
  'https://picsum.photos/800/600?random=7',
  'https://picsum.photos/800/600?random=8'
])

// 使用智能预览
function handleSmartPreview(index: number) {
  previewImages(sampleImages.value, index)
}

// 使用Vant预览
function handleVantPreview(index: number) {
  const urls = sampleImages.value.map(item => item.url)
  previewImagesWithVant(urls, index)
}

// 使用自定义页面预览
function handleCustomPreview(index: number) {
  previewImagesWithCustomPage(sampleImages.value, index)
}

// 预览简单图片
function handleSimplePreview(index: number) {
  previewImages(simpleImages.value, index)
}
</script>

<template>
  <div class="test-page">
    <VanNavBar title="图片预览测试" />
    
    <div class="page-content">
      <VanCell-group title="复杂图片预览（带描述、时间等信息）">
        <div class="image-grid">
          <div
            v-for="(image, index) in sampleImages"
            :key="index"
            class="image-item"
          >
            <img :src="image.url" class="thumbnail" @click="handleSmartPreview(index)" />
            <div class="image-info">
              <div class="image-desc">{{ image.description || '无描述' }}</div>
              <div class="image-time">{{ image.time || '无时间' }}</div>
              <div class="image-status" :class="{ qualified: image.isQualified }">
                {{ image.isQualified ? '合格' : '不合格' }}
              </div>
            </div>
          </div>
        </div>
      </VanCell-group>

      <VanCell-group title="预览方式选择">
        <VanCell title="智能预览（自动选择）" is-link @click="handleSmartPreview(0)" />
        <VanCell title="Vant预览（简单）" is-link @click="handleVantPreview(0)" />
        <VanCell title="自定义页面预览（完整功能）" is-link @click="handleCustomPreview(0)" />
      </VanCell-group>

      <VanCell-group title="简单图片预览">
        <div class="simple-grid">
          <img
            v-for="(url, index) in simpleImages"
            :key="index"
            :src="url"
            class="simple-thumbnail"
            @click="handleSimplePreview(index)"
          />
        </div>
      </VanCell-group>
    </div>
  </div>
</template>

<style lang="less" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-content {
  padding: 16px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

.image-item {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail {
  width: 100%;
  height: 120px;
  object-fit: cover;
  cursor: pointer;
}

.image-info {
  padding: 8px;
}

.image-desc {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.image-time {
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}

.image-status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  background-color: #ff4444;
  color: #fff;

  &.qualified {
    background-color: #00996B;
  }
}

.simple-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 16px;
}

.simple-thumbnail {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}
</style>
