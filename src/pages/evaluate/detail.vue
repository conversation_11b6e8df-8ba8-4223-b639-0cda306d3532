<script lang="ts" setup>
import { V1EvaluateTaskOrderInfoIdPost, V1EvaluateTaskSubmitPost } from '@/api/api.req'
import { EvaluateResultOptions, EvaluateStatus, EvaluateStatusOptions } from '@/enums/evaluate'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useLoading } from '@/hooks/useLoading'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { getOptionLabel } from '@/utils'
import { showToast, type PickerColumn } from 'vant'
import type { FormExpose } from 'vant/es/form/types'

const query = useRouteQuery<{
  id?: string
  readonly?: '0' | '1'
}>()

const formRef = ref<FormExpose>()
const isReadonly = computed(() => query.readonly === '1')
const router = useRouter()

const state = reactive({
  data: {} as Record<string, string>,
  result: undefined as undefined | string,
  duration: undefined as undefined | number,
})

const detailApi = useAsyncData(async () => {
  const resp = await V1EvaluateTaskOrderInfoIdPost({ id: query.id! })

  resp.resultList?.forEach((item) => {
    state.data[item.evaluateProject] = item.result?.toString()
  })

  state.result = resp.result?.toString()
  state.duration = resp.status?.toString() === EvaluateStatus.PENDING ? undefined : resp.duration

  return resp
}, {})

detailApi.load()

const data = computed(() => detailApi.data.value)

const ResultOptions: PickerColumn = EvaluateResultOptions.map((item) => ({
  ...item,
  text: item.label,
}))

const onSubmit = useLoading(async () => {
  await formRef.value?.validate()

  const _data = {
    id: data.value.id!,
    resultList: Object.entries(state.data).map((item) => ({
      evaluateProject: item[0],
      result: item[1],
    })),
    duration: 0,
    result: state.result,
  }

  await V1EvaluateTaskSubmitPost(_data as any)
  showToast(`评定完成`)
  router.back()
})
</script>

<template>
  <VanNavBar title="评定详情" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="page-content page-content-padding">
    <CardBox>
      <div class="flex">
        <div class="title flex-1 truncate font-bold">{{ data?.evaluateName }}</div>
        <div>
          <ColorTag
            :type="data?.status === 1 ? 'success' : data?.status === 2 ? 'danger' : 'warning'"
          >
            {{ getOptionLabel(EvaluateStatusOptions, data?.status?.toString()) }}
          </ColorTag>
        </div>
      </div>
      <div class="mt-2 space-y-2 text-sm">
        <div class="flex justify-between text-gray-600">
          <span>评估对象：{{ data?.assessorName }}</span>
          <span>{{ data.assessorOrgNames?.join('-') }}</span>
        </div>

        <div class="flex">
          <div>评估人员：</div>
          <div class="flex-1">
            <div v-for="item in data.reviewerList">
              <span>{{ item.userName }}</span>
            </div>
          </div>
          <!--  -->
        </div>
      </div>
    </CardBox>

    <CardBox class="mt-4">
      <VanForm ref="formRef">
        <div class="flex flex-col">
          <div>评估内容</div>
          <div v-for="(item, index) in data.resultList" :key="index">
            <PickerField
              :disabled="isReadonly"
              v-model="state.data[item.evaluateProject]"
              :label="item.evaluateProject"
              :columns="ResultOptions"
              :rules="[{ required: true, message: `请选择` }]"
            />
          </div>
        </div>
        <PickerField
          :disabled="isReadonly"
          v-model="state.result"
          label="评估结果"
          :rules="[{ required: true, message: `请选择` }]"
          :columns="ResultOptions"
        />
        <VanField
          :disabled="isReadonly"
          label="时长（分钟）"
          v-model="state.duration"
          placeholder="请输入"
          type="number"
          :rules="[{ required: true, message: `请输入` }]"
        />
      </VanForm>
    </CardBox>
  </div>
  <FixedButton
    @click="onSubmit"
    type="primary"
    :disabled="isReadonly"
    :loading="onSubmit.isLoading"
  >
    提交
  </FixedButton>
</template>

<style lang="less" scoped></style>
