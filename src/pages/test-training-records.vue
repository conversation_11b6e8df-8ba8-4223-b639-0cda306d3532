<script lang="ts" setup>
import TrainingRecords from '@/components/TrainingRecords.vue'

// 模拟训练记录数据
const mockRecords = ref([
  {
    date: '2024-01-15',
    workPassRate: '95',
    workCount: '12',
    workFailCount: '1',
    actionRate: '88',
    actionFailCount: '3',
    images: [
      {
        imageUrl: 'https://picsum.photos/240/128?random=1',
        description: '标准动作示例',
        iconImg: '',
        time: '09:30',
        isQualified: true,
        recordDetailAlarmId: 'alarm_001',
        loaded: true
      },
      {
        imageUrl: 'https://picsum.photos/240/128?random=2',
        description: '不规范动作',
        iconImg: 'https://picsum.photos/44/44?random=icon1',
        time: '10:15',
        isQualified: false,
        recordDetailAlarmId: 'alarm_002',
        loaded: true
      },
      {
        imageUrl: 'https://picsum.photos/240/128?random=3',
        description: '操作完成',
        iconImg: '',
        time: '11:00',
        isQualified: true,
        recordDetailAlarmId: 'alarm_003',
        loaded: true
      }
    ]
  },
  {
    date: '2024-01-14',
    workPassRate: '92',
    workCount: '8',
    workFailCount: '2',
    actionRate: '85',
    actionFailCount: '4',
    images: [
      {
        imageUrl: 'https://picsum.photos/240/128?random=4',
        description: '训练场景1',
        iconImg: '',
        time: '14:20',
        isQualified: true,
        recordDetailAlarmId: 'alarm_004',
        loaded: true
      },
      {
        imageUrl: 'https://picsum.photos/240/128?random=5',
        description: '违规操作',
        iconImg: 'https://picsum.photos/44/44?random=icon2',
        time: '15:30',
        isQualified: false,
        recordDetailAlarmId: 'alarm_005',
        loaded: true
      }
    ]
  },
  {
    date: '2024-01-13',
    workPassRate: '100',
    workCount: '5',
    workFailCount: '0',
    actionRate: '100',
    actionFailCount: '0',
    images: []
  }
])
</script>

<template>
  <div class="page">
    <VanNavBar title="训练记录测试" left-arrow @click-left="$router.back()" />
    
    <div class="page-content">
      <TrainingRecords 
        :records="mockRecords" 
        title="考核记录"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-content {
  padding: 16px 0;
}
</style>
