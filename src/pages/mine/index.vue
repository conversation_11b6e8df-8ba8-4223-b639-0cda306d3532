<script lang="ts" setup>
import dayjs from 'dayjs'
import { V1LocationHomeMine, V1LocationHomeUserDataUserId } from '@/api/api.req'
import Footer from '@/components/layOut/footer.vue'
import QrScanner from '@/components/QrScanner/index.vue'
import { TrainingProjectType, TrainingStatus } from '@/enums/training'
import { useAsyncData } from '@/hooks/useAsyncData'
import { gucStore } from '@/integration/guc'
import { currentUserInfo } from '@/store/sysUser'
import { EvaluateTypeQuery } from '../evaluate/index.vue'
import { LearningTabId } from '../learning/index.vue'

const router = useRouter()

const userInfoApi = useAsyncData(
  () => V1LocationHomeUserDataUserId({ userId: currentUserInfo.value.id! }),
  {},
)

const detailDataApi = useAsyncData(V1LocationHomeMine, {})

const userBasicInfo = computed(() => userInfoApi.data.value)

fetchInitData()

function fetchInitData() {
  userInfoApi.load()
  detailDataApi.load()
}

async function loginOut() {
  await gucStore.logoutConfirm()
  router.push('/login')
}

const detailListData = computed(() => {
  const d = detailDataApi.data.value
  return [
    {
      title: '学习',
      links: [
        {
          title: `学习时长：${dayjs.duration(+d.learningDuration! || 0, 's').format('HH:mm:ss')}(${d.learningNum || 0}次)`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/learning',
              query: {
                tab: LearningTabId.LEARNED,
              },
            })
          },
        },
        {
          title: `剩余学习任务：${d.remainLearningNum || 0}次`,
          link: `查看任务`,
          onClick() {
            router.push('/learning')
          },
        },
      ],
    },
    {
      title: '技能训练',
      links: [
        {
          title: `训练时长：${dayjs.duration(+d.trainDuration! || 0, 's').format('HH:mm:ss')}(${d.trainingNum || 0}次)`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Training,
                status: [
                  TrainingStatus.TO_BE_CHECK,
                  TrainingStatus.COMPLETED,
                  TrainingStatus.CHECKED,
                ],
              },
            })
          },
        },
        {
          title: `剩余训练任务：${d.remainTrainNum || 0}次`,
          link: `查看任务`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Training,
                status: [TrainingStatus.ING, TrainingStatus.TO_BE],
              },
            })
          },
        },
      ],
    },
    {
      title: '技能考核',
      links: [
        {
          title: `考核次数：${d.checkNum || 0}次`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Exam,
                status: [TrainingStatus.FAILED_CHECK, TrainingStatus.PASSED_CHECK],
              },
            })
          },
        },
        {
          title: `剩余考核任务：${d.remainCheckNum || 0}次`,
          link: `查看任务`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Exam,
                status: [TrainingStatus.ING, TrainingStatus.TO_BE],
              },
            })
          },
        },
      ],
    },
    {
      title: '技能竞赛',
      links: [
        {
          title: `竞赛次数：${d.raceNum || 0}次`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Race,
              },
            })
          },
        },
      ],
    },
    {
      title: '评定',
      links: [
        {
          title: `评定他人次数：${d.evaluateNum || 0}次`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/evaluate',
              query: {
                type: EvaluateTypeQuery.Reviewer,
              },
            })
          },
        },
        {
          title: `被评定次数：${d.assessedNum || 0}次`,
          link: `查看记录`,
          onClick() {
            router.push({
              path: '/evaluate',
            })
          },
        },
      ],
    },
  ]
})
</script>

<template>
  <VanNavBar title="我的">
    <template #right>
      <QrScanner
        button-text=""
        button-type="default"
        button-size="small"
        button-class="!p-0 !border-none !bg-transparent"
      />
    </template>
  </VanNavBar>
  <div class="page-content page-content-padding">
    <div class="card-item flex rounded-8px bg-[#fff] px-8px py-10px">
      <div class="size-45px overflow-hidden rounded-50% bg-[#ccc]">
        <img class="h-full w-full" src="@/assets/guc-avatar.svg" />
      </div>
      <div class="userInfo-name ml-4">
        <div>
          <div class="flex items-center gap-2">
            <span>
              {{ userBasicInfo.userName }}
            </span>
            <span class="text-sm text-gray">
              {{ userBasicInfo.jobName }} : L{{ currentUserInfo.level || 0 }}
            </span>
          </div>
          <div class="text-sm text-gray-5">
            {{ userBasicInfo.orgList?.map((n) => n.name).join('/') }}
          </div>
        </div>
      </div>
      <div>
        <VanButton class="bg-[#fff] color-[red]!" block @click="loginOut"> 退出登录 </VanButton>
      </div>
    </div>
    <div class="c-exam-list pt-4">
      <div class="flex flex-col gap-2">
        <div v-for="item in detailListData" class="detail-item">
          <CardBox>
            <div class="detail-item-title mb-1 font-bold">
              {{ item.title }}
            </div>
            <div class="detail-item-links text-sm">
              <div v-for="link in item.links" class="detail-item-link flex">
                <div class="flex-1 truncate">
                  {{ link.title }}
                </div>
                <div class="link text-green-5" @click="link.onClick">
                  {{ link.link }}
                </div>
              </div>
            </div>
          </CardBox>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.card-item {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
}

.userInfo-name {
  @apply font-size-17px color-[#666] flex flex-1 items-center;
}
</style>
