<script lang="ts" setup>
import { watchImmediate } from '@vueuse/core'
import {
  V1MobileHomeExamRecordHistoryPaperIdUserId,
  V1MobileHomeExamRecordId,
  V1MobileHomeLastedExamRecordPaperId,
  V1MobileStudyExamRecordStartPost,
} from '@/api/api.req'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useCurrentUser } from '@/store/sysUser'

const router = useRouter()

const route = useRoute()

const paperId = computed(() => route.query.id as string)

const userInfo = useCurrentUser()

const paperDataApi = useAsyncData(V1MobileHomeExamRecordId, {})

const historyRecordApi = useAsyncData(V1MobileHomeExamRecordHistoryPaperIdUserId, [])

const paperData = computed(() => paperDataApi.data?.value.paper)

watchImmediate(paperId, async () => {
  if (!paperId.value) return

  await paperDataApi.load({
    id: paperId.value,
  })

  if (!paperData.value?.id || !userInfo.sysUser?.id) return

  await historyRecordApi.load({
    userId: userInfo.sysUser.id,
    paperId: paperData.value.id,
  })
})

async function gotoExamDetail() {
  const paperId = paperData.value?.id

  if (!paperId) return

  const resp = await V1MobileHomeLastedExamRecordPaperId({
    paperId,
  })

  const id = resp.id!

  /**
   * 开始训练后端记录初始信息
   */
  await V1MobileStudyExamRecordStartPost({
    id,
  })

  router.push({
    path: '/exam/start-exam',
    query: {
      id,
    },
  })
}

const otherInfo = computed(() => {
  const totalScore = paperData.value?.totalScore ?? '-'

  const remainRetryTimes =
    (paperData.value?.retryTimes || 0) - (historyRecordApi.data.value.length - 1)

  return {
    totalScore,
    remainRetryTimes,
  }
})

function goBack() {
  router.back()
}
</script>

<template>
  <VanNavBar title="试卷详情" left-arrow left-text="返回" @click-left="goBack" />
  <div class="card-item mx-4 my-4 rounded-8px bg-[#fff] px-3 py-2">
    <div class="flex py-3 gap-2">
      <div class="h-22px w-22px flex items-center justify-center rounded-50% bg-[#04D696]">
        <div class="h-16px w-16px text-white">
          <SvgIcon name="svg/test-paper-icon" size="16" />
        </div>
      </div>
      <div class="flex-1 text-17px color-[#666] font-500">
        <VanTextEllipsis :content="paperData?.name || '-'">
          <template #action="{ expanded }">
            <VanIcon v-if="!expanded" name="arrow-down" />
            <VanIcon v-else name="arrow-up" />
          </template>
        </VanTextEllipsis>
      </div>
    </div>
    <div v-if="paperData?.questionPropertyList?.length" class="flex gap-12px pb-8px">
      <VanTag
        v-for="(tag, idx) in paperData?.questionPropertyList"
        :key="`${idx}_${tag.columnValue}`"
        color="#E7E7E7"
        text-color="#333"
        size="large"
      >
        {{ tag.columnValue }}
      </VanTag>
    </div>
    <div
      class="flex flex-col gap-2 border-1px border-color-[#f4f6f8] border-t-solid pt-2 font-size-14px color-[#999]"
    >
      <div>
        {{ paperData?.limitBegTime || '-' }} -
        {{ paperData?.limitEndTime || '-' }}
      </div>

      <div class="flex">
        <div class="flex-1">
          满分：
          {{ otherInfo.totalScore }}
        </div>
        <div class="flex-1">
          合格分数：
          {{ paperData?.standardScore || '-' }}
        </div>
      </div>
    </div>
    <div class="title mb-2 mt-6 flex">
      <span class="mr-2 flex-1 font-bold"> 考试记录 </span>
      <span class="font-size-14px color-[#999]">
        剩余考试次数：{{ otherInfo.remainRetryTimes }}
      </span>
    </div>
    <div
      v-if="historyRecordApi.data.value.length"
      class="record-list flex flex-col gap-2 font-size-14px color-[#999]"
    >
      <div v-for="item in historyRecordApi.data.value" :key="item.id" class="record-item flex">
        <div class="flex-1">
          {{ item.begTime }}
        </div>
        <div class="">{{ item.score }}分</div>
      </div>
    </div>
    <EmptyData v-else />
  </div>
  <div class="fixed bottom-0 box-border w-full bg-white p-2">
    <VanButton
      type="primary"
      block
      :disabled="otherInfo.remainRetryTimes <= 0"
      @click="gotoExamDetail"
    >
      {{ historyRecordApi.data.value.length ? '继续' : '开始' }}考试
    </VanButton>
  </div>
</template>

<style lang="less" scoped>
.card-item {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
}
</style>
