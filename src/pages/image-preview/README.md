# 图片预览组件 - 项目兼容版本

## 概述

这是一个从uniapp改造为项目兼容的图片预览组件，使用Vue 3 + Vite + Vant技术栈，提供了完整的图片预览功能。

## 主要改动

### 1. 技术栈迁移
- **从uniapp迁移到Vue 3 + Vite + Vant**
- 移除了所有uniapp特有的API和组件
- 使用标准的Web API和Vant组件
- 支持现代浏览器环境

### 2. 组件和API替换
- `uni.getStorageSync/setStorageSync` → `sessionStorage`
- `uni.navigateBack` → `router.back()`
- `uni.showLoading/hideLoading` → `showLoadingToast/closeToast`
- `uni.showToast` → `showSuccessToast/showFailToast`
- `uni.showActionSheet` → 自定义操作菜单
- `swiper/swiper-item` → `VanSwipe/VanSwipeItem`
- `image` → `img`
- `view` → `div`
- `text` → `span`
- `uni-icons` → `VanIcon`

### 3. 事件处理更新
- `@tap` → `@click`
- `@longpress` → `@contextmenu.prevent`
- 移除了uniapp特有的事件参数结构

### 4. 样式系统更新
- `scss` → `less`
- 移除了uniapp特有的单位（如rpx）
- 添加了Web环境的交互样式（cursor: pointer等）
- 优化了响应式设计

### 5. 功能增强
- 智能图片预览工具函数
- 支持多种预览方式（Vant简单预览 / 自定义完整预览）
- 更好的错误处理和用户反馈
- 现代化的操作菜单设计

## 功能特性

### 基础功能
- ✅ 图片轮播预览（使用VanSwipe）
- ✅ 图片自适应显示（object-fit: contain）
- ✅ 图片计数显示
- ✅ 图片描述和时间显示
- ✅ 状态图标显示（合格/不合格）

### 交互功能
- ✅ 点击图片切换导航栏显示
- ✅ 右键菜单显示操作选项
- ✅ 下载图片到本地
- ✅ 图片分享功能（支持Web Share API）
- ✅ 图片加载失败重试
- ✅ 路由导航返回

### 视觉效果
- ✅ Vant Loading组件加载动画
- ✅ 错误状态显示
- ✅ 导航栏平滑隐藏/显示动画
- ✅ 响应式设计
- ✅ 现代化操作菜单设计

## 使用方法

### 1. 使用工具函数（推荐）

```typescript
import { previewImages, previewImagesWithCustomPage, previewImagesWithVant } from '@/utils/imagePreview'
import type { ImagePreviewItem } from '@/utils/imagePreview'

// 准备图片数据
const images: ImagePreviewItem[] = [
  {
    url: 'https://example.com/image1.jpg',
    description: '图片描述',
    time: '2024-01-15 10:30:00',
    iconImg: 'https://example.com/icon.png',
    isQualified: true
  },
  // ... 更多图片
]

// 智能预览（自动选择最佳预览方式）
previewImages(images, 0)

// 强制使用自定义页面预览
previewImagesWithCustomPage(images, 0)

// 使用Vant简单预览
const urls = images.map(item => item.url)
previewImagesWithVant(urls, 0)
```

### 2. 直接路由跳转

```typescript
import { router } from '@/router'

function previewImages(images: ImagePreviewItem[], startIndex = 0) {
  try {
    // 保存数据到sessionStorage
    sessionStorage.setItem('previewImageData', JSON.stringify(images))
    sessionStorage.setItem('previewCurrentIndex', startIndex.toString())

    // 跳转到预览页面
    router.push('/image-preview')
  } catch (error) {
    console.error('预览图片失败:', error)
    showFailToast('预览失败')
  }
}
```

### 3. 在组件中使用

```vue
<template>
  <div class="image-list">
    <img
      v-for="(image, index) in images"
      :key="index"
      :src="image.url"
      @click="handlePreview(index)"
    />
  </div>
</template>

<script setup lang="ts">
import { previewImages } from '@/utils/imagePreview'

const images = ref([...])

function handlePreview(index: number) {
  previewImages(images.value, index)
}
</script>
```

### 4. 测试页面
访问 `/test-image-preview` 查看完整的使用示例和功能演示。

## 数据结构

### ImageItem 接口
```typescript
interface ImageItem {
  url: string              // 图片URL（必需）
  description?: string     // 图片描述（可选）
  time?: string           // 时间信息（可选）
  iconImg?: string        // 状态图标URL（可选）
  isQualified?: boolean   // 是否合格（可选）
}
```

## 工具函数说明

### previewImages(images, startIndex)
智能图片预览函数，会根据图片数据自动选择最佳的预览方式：
- 如果是字符串数组，使用Vant的ImagePreview
- 如果是对象数组且包含额外信息，使用自定义页面预览
- 否则使用Vant的ImagePreview

### previewImagesWithVant(urls, startIndex)
使用Vant的ImagePreview组件进行简单预览，适合只需要基本预览功能的场景。

### previewImagesWithCustomPage(images, startIndex)
使用自定义页面进行完整功能预览，支持描述、时间、状态图标等额外信息。

## 样式定制

组件使用 Less 编写样式，支持以下定制：

### 主题色彩
- 背景色：`#000`（黑色背景）
- 文字色：`#fff`（白色文字）
- 半透明遮罩：`rgba(0, 0, 0, 0.5)`
- 主题色：继承项目主题色 `#00996B`

### 响应式断点
- 移动端：`max-width: 768px`
- 自动适配不同屏幕尺寸

## 注意事项

1. **存储方式**: 使用sessionStorage替代uniapp的缓存，页面关闭后自动清理
2. **错误处理**: 包含完善的错误处理和用户反馈机制
3. **性能考虑**: 支持图片懒加载和加载状态管理
4. **浏览器兼容**: 需要现代浏览器支持（支持ES6+、fetch、sessionStorage等）
5. **下载功能**: 使用Web标准的下载方式，支持跨域图片下载
6. **分享功能**: 优先使用Web Share API，降级到剪贴板复制

## uni-app 适配说明

### 已完成的适配工作

1. **事件处理适配**
   - 将 `@click` 改为 `@tap`（uni-app 推荐用法）
   - 保留了 `@longpress` 等 uni-app 特有事件

2. **组件标签适配**
   - 使用 `<view>` 替代 `<div>`
   - 使用 `<text>` 包裹文本内容
   - 使用 `<image>` 标签并正确闭合
   - 移除了 `cursor: pointer` 等 H5 专用样式

3. **样式优化**
   - 移除了不兼容的 CSS 属性
   - 优化了按钮样式，使用 `view` + `text` 结构
   - 调整了 `:active` 伪类的使用

4. **uni-app 组件使用**
   - 正确使用 `<swiper>` 和 `<swiper-item>`
   - 使用 `<uni-icons>` 图标组件
   - 遵循 uni-app 的组件规范

### 兼容性

- ✅ uni-app 框架
- ✅ Vue 3 + TypeScript
- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ H5 页面
- ✅ App 端（iOS/Android）
- ✅ 快应用

### 注意事项

1. **小程序兼容性**
   - 避免使用 `innerHTML` 等 DOM 操作
   - 使用 uni-app 提供的 API 替代浏览器 API
   - 样式中避免使用小程序不支持的 CSS 属性

2. **性能优化**
   - 图片懒加载在小程序中自动生效
   - 避免频繁的 setData 操作
   - 合理使用 `v-if` 和 `v-show`

## 更新日志

### v2.1.0 (当前版本)
- **uni-app 适配**: 完全适配 uni-app 多端开发
- 事件处理改为 `@tap`
- 组件标签规范化
- 样式兼容性优化
- 移除浏览器专用属性

### v2.0.0
- 重构为 TypeScript
- 添加完整的状态管理
- 优化用户交互体验
- 添加错误处理机制
- 改进样式和动画效果

### v1.0.0 (原版本)
- 基础图片预览功能
- 简单的轮播和显示
