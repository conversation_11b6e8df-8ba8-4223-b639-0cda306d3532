<template>
  <div class="competition-detail min-h-screen bg-gray-50">
    <VanNavBar title="比赛详情" left-arrow @click-left="$router.back()" />

    <div class="page-content">
      <!-- 标签导航栏 -->
      <div class="tab-nav bg-white border-b border-gray-200">
        <VanTabs
          v-model:active="activeTabIndex"
          @change="handleTabChange"
          swipeable
          sticky
          offset-top="46"
        >
          <VanTab
            v-for="(tab, index) in tabs"
            :key="tab.id"
            :title="tab.projectName"
            :name="index"
          />
        </VanTabs>
      </div>

      <!-- 比赛信息区域 -->
      <div class="competition-info px-4 py-4">
        <CardBox>
          <div class="section-title flex items-center mb-4">
            <span class="text-lg font-medium text-gray-800">比赛信息</span>
          </div>

          <div class="stats-grid grid grid-cols-3 gap-4">
            <!-- 左侧卡片 -->
            <div class="stat-card rounded-lg p-4 bg-white border border-gray-100">
              <div class="flex flex-col gap-4 items-center">
                <!-- 当前排名 -->
                <div class="text-center">
                  <div class="text-sm text-gray-500 mb-1">当前排名</div>
                  <div class="text-lg font-semibold text-primary">
                    第 {{ competitionData.ranking || '-' }} 名
                  </div>
                </div>
                <!-- 考核用时 -->
                <div class="text-center">
                  <div class="text-sm text-gray-500 mb-1">考核用时</div>
                  <div class="text-lg font-semibold text-primary">
                    {{ competitionData.duration || '0:00' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧卡片 -->
            <div class="stat-card bg-white rounded-lg p-4 col-span-2 border border-gray-100">
              <div class="space-y-3">
                <!-- 考核时间 -->
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500 w-20">考核时间</span>
                  <span class="text-sm text-gray-800">{{ competitionData.examTime || '-' }}</span>
                </div>
                <div class="h-px bg-gray-200"></div>

                <!-- 考核要求 -->
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500 w-20">考核要求</span>
                  <span class="text-sm text-gray-800 flex-1 text-right">
                    {{ competitionData.requirement || '-' }}
                  </span>
                </div>
                <div class="h-px bg-gray-200"></div>

                <!-- 有效作业次数 -->
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500 w-20">有效作业次数</span>
                  <span class="text-sm text-gray-800">
                    {{ competitionData.validOperations || 0 }} 次
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardBox>
      </div>

      <!-- 比赛记录列表 -->
      <TrainingRecords :records="competitionRecords" title="比赛记录" />

      <!-- 空数据处理 -->
      <NoData v-if="competitionRecords.length === 0" size="small" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { V1LocationStudyRaceProjectIdUserId, V1ManageTrainStudyRecordsPagePost } from '@/api/api.req'
import { formatDuration } from '@/utils/time'
import { fetchTabDataCommon, type TrainingRecord } from '@/hooks/useTrainingData'
import TrainingRecords from '@/components/TrainingRecords.vue'
import NoData from '@/components/NoData.vue'
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '@/api/api.model'

// 获取路由参数
const route = useRoute()
const userId = ref(route.query.userId as string || '')

// 选项卡配置
const tabs = ref<V1LocationStudyCheckProjectIDUserIDGetResponseResult[]>([])
const activeTab = ref('')
const activeTabIndex = ref(0)

// 比赛数据
const competitionData = ref({
    examTime: '',
    requirement: '',
    ranking: 0,
    duration: '',
    validOperations: 0,
})

// 比赛记录列表
const competitionRecords = ref<TrainingRecord[]>([])

// 获取标签数据
async function fetchTabs() {
    try {
        const response = await V1ManageTrainStudyRecordsPagePost({
            data: {
                projectType: 3, // 比赛项目类型为3
                userId: userId.value
            }
        })
        if (response?.records) {
            tabs.value = response.records || [];
            if (tabs.value.length > 0 && !activeTab.value) {
                activeTab.value = tabs.value[0].id || ''
                activeTabIndex.value = 0
                await fetchTabData(tabs.value[0].id || '')
            }
            const currProject = tabs.value.find(item => item.id === activeTab.value);
            // 获取比赛信息
            if (currProject?.projectId) {
                await fetchCompetitionInfo(currProject.projectId)
            }
        }
    } catch (error) {
        console.error('获取标签数据失败:', error)
    }
}

// 切换选项卡
function handleTabChange(index: number) {
    if (tabs.value[index]) {
        const tab = tabs.value[index]
        activeTab.value = tab.id || ''
        activeTabIndex.value = index
        fetchTabData(tab.id || '')
        if (tab.projectId) {
            fetchCompetitionInfo(tab.projectId)
        }
    }
}

// 获取比赛信息
async function fetchCompetitionInfo(projectId: string) {
    if (!projectId) return

    try {
        const raceResponse = await V1LocationStudyRaceProjectIdUserId({
            projectId,
            userId: userId.value
        })

        // 更新比赛数据
        if (raceResponse) {
            const raceData = raceResponse
            competitionData.value = {
                examTime: raceData.lastUpdateTime ? new Date(raceData.lastUpdateTime).toLocaleString() : '',
                requirement:
                    raceData.raceType == '1' ? `在 ${raceData.requestDuration || 0} 分钟内完成`
                        : `完成 ${raceData.requestFrequency || 0} 次操作`,
                duration: formatDuration(raceData.trainDurationSecond || 0),
                validOperations: raceData.opNumOk || 0,
                ranking: raceData.ranking || 0,
            }
        }
    } catch (error) {
        console.error('获取比赛信息失败:', error)
    }
}

// 获取比赛记录数据
async function fetchTabData(tabId: string) {
    if (!tabId) return
    await fetchTabDataCommon(tabId, (records) => {
        competitionRecords.value = records
    })
}

// 页面初始化
onMounted(async () => {
    // 从路由参数获取初始数据
    if (route.query.id) {
        activeTab.value = route.query.id as string
    }

    await fetchTabs()

    if (route.query.id) {
        await fetchTabData(route.query.id as string)
        // 设置当前选中的标签索引
        const index = tabs.value.findIndex(item => item.id === route.query.id)
        if (index >= 0) {
            activeTabIndex.value = index
        }
    }
})
</script>

<style lang="less" scoped>
.competition-detail {
  .stat-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }
}
</style>