# 训练组件页面迁移文档

## 概述

本次迁移将三个uniapp训练详情页面转换为适配当前Vue3 + Vant + UnoCSS项目的页面。

## 迁移内容

### 页面转换

1. **训练报告详情页面** (`src/pages/training/report-detail/index.vue`)
   - 从uniapp语法转换为Vue3 + Vant
   - 使用VanTabs替代原始的横向滚动标签导航
   - 集成环形进度条和训练记录组件

2. **考核详情页面** (`src/pages/training/exam-detail/index.vue`)
   - 转换考核信息展示区域
   - 集成数据统计和记录列表功能
   - 支持考核状态显示

3. **比赛详情页面** (`src/pages/training/competition-detail/index.vue`)
   - 转换比赛信息和排名显示
   - 支持比赛记录查看功能

### 新增组件

1. **CircleProgress** (`src/components/CircleProgress.vue`)
   - 替代原始的`dc-ring-timing`组件
   - 支持chart和timing两种模式
   - 使用CSS动画实现进度效果

2. **TrainingRecords** (`src/components/TrainingRecords.vue`)
   - 替代原始的`dc-training-records`组件
   - 时间轴样式的记录列表
   - 支持图片懒加载和横向滚动
   - 集成图片预览功能

3. **NoData** (`src/components/NoData.vue`)
   - 替代原始的`dc-no-data`组件
   - 支持多种尺寸配置
   - 使用项目统一的空状态样式

### 工具函数

1. **useTrainingData** (`src/hooks/useTrainingData.ts`)
   - 提供训练数据获取的通用函数
   - 包含数据格式化和类型定义

2. **formatDuration** (`src/utils/time.ts`)
   - 新增时长格式化函数
   - 支持秒数转换为可读时长格式

## 路由更新

### 自动路由生成
项目使用`unplugin-vue-router`自动生成路由：
- `/training/report-detail/` - 训练报告详情
- `/training/exam-detail/` - 考核详情  
- `/training/competition-detail/` - 比赛详情

### 导航逻辑更新
更新了以下页面的导航逻辑：

1. **训练列表页面** (`src/pages/training/index.vue`)
   - 根据projectType跳转到对应详情页面
   - 传递userId参数

2. **首页** (`src/pages/home/<USER>
   - 待考核项目点击跳转到对应详情页面

## 技术栈对比

| 功能 | 原uniapp实现 | 新Vue3实现 |
|------|-------------|-----------|
| 标签导航 | `<scroll-view>` | `<VanTabs>` |
| 圆环进度 | `dc-ring-timing` | `CircleProgress` |
| 记录列表 | `dc-training-records` | `TrainingRecords` |
| 空状态 | `dc-no-data` | `NoData` |
| 样式系统 | rpx + 内联样式 | UnoCSS工具类 |
| 路由 | uni.navigateTo | Vue Router |

## 使用方法

### 访问页面
```typescript
// 跳转到训练报告详情
router.push({
  path: '/training/report-detail',
  query: { id: 'projectId', userId: 'userId' }
})

// 跳转到考核详情
router.push({
  path: '/training/exam-detail', 
  query: { id: 'projectId', userId: 'userId' }
})

// 跳转到比赛详情
router.push({
  path: '/training/competition-detail',
  query: { id: 'projectId', userId: 'userId' }
})
```

### 使用组件
```vue
<template>
  <!-- 环形进度条 -->
  <CircleProgress 
    :value="75" 
    active-color="#00996B"
    mode="chart"
  />
  
  <!-- 训练记录列表 -->
  <TrainingRecords 
    :records="records"
    title="训练记录"
  />
  
  <!-- 空数据状态 -->
  <NoData 
    tip="暂无训练记录"
    size="normal"
  />
</template>
```

## 测试

运行组件测试：
```bash
npm run test src/components/__tests__/TrainingComponents.test.ts
```

## 注意事项

1. 确保API接口`V1ManageTrainStudyRecordsPagePost`等已正确配置
2. 图片资源路径需要根据实际情况调整
3. 样式可能需要根据设计稿进行微调
4. 建议在不同设备上测试响应式效果
